<template>
  <div class="sidebar-menu">
    <!-- 项目信息 -->
    <div class="project-info" v-if="projectStore.hasCurrentProject && !appStore.sidebarCollapsed">
      <div class="project-title">{{ projectStore.currentProject?.title }}</div>
      <div class="project-progress">
        <el-progress 
          :percentage="projectStore.progressPercentage" 
          :stroke-width="4"
          :show-text="false"
        />
        <span class="progress-text">{{ projectStore.completedChapters }}/{{ projectStore.currentProject?.totalChapters }} 章</span>
      </div>
    </div>

    <!-- 菜单分组 -->
    <div class="menu-groups">
      <div 
        v-for="(routes, category) in menuRoutes" 
        :key="category"
        class="menu-group"
      >
        <div class="group-title" v-if="!appStore.sidebarCollapsed">{{ category }}</div>
        
        <div class="menu-items">
          <router-link
            v-for="route in routes"
            :key="route.name"
            :to="route.path"
            class="menu-item"
            :class="{ active: $route.name === route.name }"
            :title="appStore.sidebarCollapsed ? route.meta?.title : ''"
          >
            <el-icon class="menu-icon">
              <component :is="route.meta?.icon || 'Document'" />
            </el-icon>
            <span class="menu-text" v-if="!appStore.sidebarCollapsed">
              {{ route.meta?.title }}
            </span>
          </router-link>
        </div>
      </div>
    </div>

    <!-- 底部操作 -->
    <div class="sidebar-footer">
      <el-button 
        v-if="!projectStore.hasCurrentProject"
        type="primary" 
        :icon="Plus"
        @click="createNewProject"
        :class="{ 'icon-only': appStore.sidebarCollapsed }"
      >
        <span v-if="!appStore.sidebarCollapsed">新建项目</span>
      </el-button>
      
      <div v-else class="project-actions">
        <el-button 
          :icon="FolderOpened"
          @click="openProject"
          :class="{ 'icon-only': appStore.sidebarCollapsed }"
          text
        >
          <span v-if="!appStore.sidebarCollapsed">打开</span>
        </el-button>
        <el-button 
          :icon="Download"
          @click="saveProject"
          :class="{ 'icon-only': appStore.sidebarCollapsed }"
          text
        >
          <span v-if="!appStore.sidebarCollapsed">保存</span>
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElIcon, ElProgress, ElButton, ElMessage } from 'element-plus'
import { Plus, FolderOpened, Download } from '@element-plus/icons-vue'
import { useAppStore } from '../stores/app'
import { useProjectStore } from '../stores/project'
import { getRoutesByCategory } from '../router'

const router = useRouter()
const appStore = useAppStore()
const projectStore = useProjectStore()

// 获取分组后的菜单路由
const menuRoutes = computed(() => {
  return getRoutesByCategory()
})

// 创建新项目
const createNewProject = () => {
  router.push('/project')
}

// 打开项目
const openProject = async () => {
  try {
    const result = await window.electronAPI.file.openProject()
    if (result.success && result.data) {
      // 处理打开的项目数据
      console.log('打开项目:', result.data)
      ElMessage.success('项目打开成功')
    } else if (result.error) {
      ElMessage.error(result.error)
    }
  } catch (error) {
    console.error('打开项目失败:', error)
    ElMessage.error('打开项目失败')
  }
}

// 保存项目
const saveProject = async () => {
  try {
    if (!projectStore.currentProject) {
      ElMessage.warning('没有可保存的项目')
      return
    }

    const projectData = {
      ...projectStore.currentProject,
      outline: projectStore.currentOutline,
      chapters: projectStore.chapters,
      characters: projectStore.characters
    }

    const result = await window.electronAPI.file.saveProject(projectData)
    if (result.success) {
      ElMessage.success('项目保存成功')
    } else if (result.error) {
      ElMessage.error(result.error)
    }
  } catch (error) {
    console.error('保存项目失败:', error)
    ElMessage.error('保存项目失败')
  }
}
</script>

<style scoped>
.sidebar-menu {
  @apply flex flex-col h-full;
}

.project-info {
  @apply p-4 mb-4 glass-panel;
}

.project-title {
  @apply text-white font-medium mb-2 truncate;
}

.project-progress {
  @apply space-y-1;
}

.progress-text {
  @apply text-xs text-white/70;
}

.menu-groups {
  @apply flex-1 overflow-auto space-y-4;
}

.menu-group {
  @apply space-y-2;
}

.group-title {
  @apply text-xs font-medium text-white/60 uppercase tracking-wider px-3;
}

.menu-items {
  @apply space-y-1;
}

.menu-item {
  @apply flex items-center px-3 py-2 rounded-lg text-white/80 hover:text-white hover:bg-white/10 transition-all duration-200 no-underline;
}

.menu-item.active {
  @apply bg-white/20 text-white;
}

.menu-icon {
  @apply text-lg flex-shrink-0;
}

.menu-text {
  @apply ml-3 truncate;
}

.sidebar-footer {
  @apply p-4 border-t border-white/20;
}

.project-actions {
  @apply flex space-x-2;
}

.icon-only {
  @apply w-10 h-10 p-0;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .sidebar-menu {
    @apply w-full;
  }
}
</style>
