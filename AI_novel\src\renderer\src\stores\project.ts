import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export interface Project {
  id?: number
  title: string
  genre: string
  theme: string
  style: string
  totalChapters: number
  wordsPerChapter: number
  createdAt?: string
  updatedAt?: string
}

export interface Outline {
  id?: number
  projectId: number
  title: string
  coreTheme: string
  storySummary: string
  worldSetting: string
  createdAt?: string
  updatedAt?: string
}

export interface Chapter {
  id?: number
  projectId: number
  chapterNumber: number
  title: string
  summary: string
  content: string
  wordCount: number
  status: 'draft' | 'completed'
  createdAt?: string
  updatedAt?: string
}

export interface Character {
  id?: number
  projectId: number
  name: string
  roleType: '主角' | '重要角色' | '配角' | '反派' | '龙套'
  identity: string
  personality: string
  background: string
  appearance: string
  createdAt?: string
  updatedAt?: string
}

export const useProjectStore = defineStore('project', () => {
  // 当前项目
  const currentProject = ref<Project | null>(null)
  
  // 项目列表
  const projects = ref<Project[]>([])
  
  // 当前大纲
  const currentOutline = ref<Outline | null>(null)
  
  // 章节列表
  const chapters = ref<Chapter[]>([])
  
  // 角色列表
  const characters = ref<Character[]>([])
  
  // 加载状态
  const isLoading = ref(false)
  
  // 计算属性
  const hasCurrentProject = computed(() => currentProject.value !== null)
  const currentProjectId = computed(() => currentProject.value?.id || null)
  const totalWordCount = computed(() => {
    return chapters.value.reduce((total, chapter) => total + chapter.wordCount, 0)
  })
  const completedChapters = computed(() => {
    return chapters.value.filter(chapter => chapter.status === 'completed').length
  })
  const progressPercentage = computed(() => {
    if (!currentProject.value || chapters.value.length === 0) return 0
    return Math.round((completedChapters.value / currentProject.value.totalChapters) * 100)
  })

  // 项目管理方法
  const setCurrentProject = (project: Project | null) => {
    currentProject.value = project
    if (project) {
      loadProjectData(project.id!)
    } else {
      clearProjectData()
    }
  }

  const createProject = async (projectData: Omit<Project, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      isLoading.value = true
      
      const result = await window.electronAPI.database.run(
        `INSERT INTO projects (title, genre, theme, style, total_chapters, words_per_chapter)
         VALUES (?, ?, ?, ?, ?, ?)`,
        [projectData.title, projectData.genre, projectData.theme, projectData.style, 
         projectData.totalChapters, projectData.wordsPerChapter]
      )

      const newProject: Project = {
        id: result.lastInsertRowid,
        ...projectData,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }

      projects.value.push(newProject)
      setCurrentProject(newProject)
      
      return newProject
    } catch (error) {
      console.error('创建项目失败:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }

  const updateProject = async (projectId: number, updates: Partial<Project>) => {
    try {
      isLoading.value = true
      
      const setClause = Object.keys(updates).map(key => `${key} = ?`).join(', ')
      const values = [...Object.values(updates), projectId]
      
      await window.electronAPI.database.run(
        `UPDATE projects SET ${setClause}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`,
        values
      )

      // 更新本地状态
      const projectIndex = projects.value.findIndex(p => p.id === projectId)
      if (projectIndex !== -1) {
        projects.value[projectIndex] = { ...projects.value[projectIndex], ...updates }
      }

      if (currentProject.value?.id === projectId) {
        currentProject.value = { ...currentProject.value, ...updates }
      }
    } catch (error) {
      console.error('更新项目失败:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }

  const deleteProject = async (projectId: number) => {
    try {
      isLoading.value = true
      
      await window.electronAPI.database.run('DELETE FROM projects WHERE id = ?', [projectId])
      
      projects.value = projects.value.filter(p => p.id !== projectId)
      
      if (currentProject.value?.id === projectId) {
        setCurrentProject(null)
      }
    } catch (error) {
      console.error('删除项目失败:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }

  const loadProjects = async () => {
    try {
      isLoading.value = true
      
      const result = await window.electronAPI.database.query(
        'SELECT * FROM projects ORDER BY updated_at DESC'
      )
      
      projects.value = result
    } catch (error) {
      console.error('加载项目列表失败:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 大纲管理方法
  const saveOutline = async (outlineData: Omit<Outline, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      if (currentOutline.value?.id) {
        // 更新现有大纲
        await window.electronAPI.database.run(
          `UPDATE outlines SET title = ?, core_theme = ?, story_summary = ?, 
           world_setting = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?`,
          [outlineData.title, outlineData.coreTheme, outlineData.storySummary, 
           outlineData.worldSetting, currentOutline.value.id]
        )
        
        currentOutline.value = { ...currentOutline.value, ...outlineData }
      } else {
        // 创建新大纲
        const result = await window.electronAPI.database.run(
          `INSERT INTO outlines (project_id, title, core_theme, story_summary, world_setting)
           VALUES (?, ?, ?, ?, ?)`,
          [outlineData.projectId, outlineData.title, outlineData.coreTheme, 
           outlineData.storySummary, outlineData.worldSetting]
        )

        currentOutline.value = {
          id: result.lastInsertRowid,
          ...outlineData,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      }
    } catch (error) {
      console.error('保存大纲失败:', error)
      throw error
    }
  }

  // 章节管理方法
  const addChapter = async (chapterData: Omit<Chapter, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      const result = await window.electronAPI.database.run(
        `INSERT INTO chapters (project_id, chapter_number, title, summary, content, word_count, status)
         VALUES (?, ?, ?, ?, ?, ?, ?)`,
        [chapterData.projectId, chapterData.chapterNumber, chapterData.title, 
         chapterData.summary, chapterData.content, chapterData.wordCount, chapterData.status]
      )

      const newChapter: Chapter = {
        id: result.lastInsertRowid,
        ...chapterData,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }

      chapters.value.push(newChapter)
      chapters.value.sort((a, b) => a.chapterNumber - b.chapterNumber)
      
      return newChapter
    } catch (error) {
      console.error('添加章节失败:', error)
      throw error
    }
  }

  const updateChapter = async (chapterId: number, updates: Partial<Chapter>) => {
    try {
      const setClause = Object.keys(updates).map(key => `${key} = ?`).join(', ')
      const values = [...Object.values(updates), chapterId]
      
      await window.electronAPI.database.run(
        `UPDATE chapters SET ${setClause}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`,
        values
      )

      const chapterIndex = chapters.value.findIndex(c => c.id === chapterId)
      if (chapterIndex !== -1) {
        chapters.value[chapterIndex] = { ...chapters.value[chapterIndex], ...updates }
      }
    } catch (error) {
      console.error('更新章节失败:', error)
      throw error
    }
  }

  // 角色管理方法
  const addCharacter = async (characterData: Omit<Character, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      const result = await window.electronAPI.database.run(
        `INSERT INTO characters (project_id, name, role_type, identity, personality, background, appearance)
         VALUES (?, ?, ?, ?, ?, ?, ?)`,
        [characterData.projectId, characterData.name, characterData.roleType, 
         characterData.identity, characterData.personality, characterData.background, characterData.appearance]
      )

      const newCharacter: Character = {
        id: result.lastInsertRowid,
        ...characterData,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }

      characters.value.push(newCharacter)
      return newCharacter
    } catch (error) {
      console.error('添加角色失败:', error)
      throw error
    }
  }

  // 加载项目数据
  const loadProjectData = async (projectId: number) => {
    try {
      isLoading.value = true

      // 加载大纲
      const outlineResult = await window.electronAPI.database.query(
        'SELECT * FROM outlines WHERE project_id = ?', [projectId]
      )
      currentOutline.value = outlineResult[0] || null

      // 加载章节
      const chaptersResult = await window.electronAPI.database.query(
        'SELECT * FROM chapters WHERE project_id = ? ORDER BY chapter_number', [projectId]
      )
      chapters.value = chaptersResult

      // 加载角色
      const charactersResult = await window.electronAPI.database.query(
        'SELECT * FROM characters WHERE project_id = ? ORDER BY name', [projectId]
      )
      characters.value = charactersResult

    } catch (error) {
      console.error('加载项目数据失败:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 清空项目数据
  const clearProjectData = () => {
    currentOutline.value = null
    chapters.value = []
    characters.value = []
  }

  return {
    // 状态
    currentProject,
    projects,
    currentOutline,
    chapters,
    characters,
    isLoading,
    
    // 计算属性
    hasCurrentProject,
    currentProjectId,
    totalWordCount,
    completedChapters,
    progressPercentage,
    
    // 方法
    setCurrentProject,
    createProject,
    updateProject,
    deleteProject,
    loadProjects,
    saveOutline,
    addChapter,
    updateChapter,
    addCharacter,
    loadProjectData,
    clearProjectData
  }
})
