import { createRouter, createWebHashHistory, RouteRecordRaw } from 'vue-router'

// 导入页面组件
const Home = () => import('../views/Home.vue')
const Welcome = () => import('../views/Welcome.vue')
const ProjectManagement = () => import('../views/ProjectManagement.vue')
const OutlineGeneration = () => import('../views/OutlineGeneration.vue')
const OutlineEdit = () => import('../views/OutlineEdit.vue')
const ChapterEdit = () => import('../views/ChapterEdit.vue')
const ChapterGeneration = () => import('../views/ChapterGeneration.vue')
const CharacterEdit = () => import('../views/CharacterEdit.vue')
const CharacterRelationship = () => import('../views/CharacterRelationship.vue')
const ChapterAnalysis = () => import('../views/ChapterAnalysis.vue')
const Statistics = () => import('../views/Statistics.vue')
const AIChat = () => import('../views/AIChat.vue')
const PromptTemplates = () => import('../views/PromptTemplates.vue')
const AIFlavorReduction = () => import('../views/AIFlavorReduction.vue')
const MemoryWindow = () => import('../views/MemoryWindow.vue')
const Settings = () => import('../views/Settings.vue')
const RunningLogs = () => import('../views/RunningLogs.vue')
const VectorSearch = () => import('../views/VectorSearch.vue')
const ContextManagement = () => import('../views/ContextManagement.vue')
const PlatformAdaptation = () => import('../views/PlatformAdaptation.vue')

// 路由配置
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Home',
    component: Home,
    children: [
      {
        path: '',
        name: 'Welcome',
        component: Welcome,
        meta: {
          title: '欢迎',
          icon: 'House',
          showInMenu: false
        }
      }
    ],
    meta: {
      title: '首页',
      icon: 'House',
      showInMenu: false
    }
  },
  {
    path: '/project',
    name: 'ProjectManagement',
    component: ProjectManagement,
    meta: {
      title: '项目管理',
      icon: 'FolderOpened',
      showInMenu: true,
      category: '项目管理'
    }
  },
  {
    path: '/outline-generation',
    name: 'OutlineGeneration',
    component: OutlineGeneration,
    meta: {
      title: '大纲生成',
      icon: 'DocumentAdd',
      showInMenu: true,
      category: '创作工具'
    }
  },
  {
    path: '/outline-edit',
    name: 'OutlineEdit',
    component: OutlineEdit,
    meta: {
      title: '大纲编辑',
      icon: 'Edit',
      showInMenu: true,
      category: '创作工具'
    }
  },
  {
    path: '/chapter-edit',
    name: 'ChapterEdit',
    component: ChapterEdit,
    meta: {
      title: '章节编辑',
      icon: 'Document',
      showInMenu: true,
      category: '创作工具'
    }
  },
  {
    path: '/chapter-generation',
    name: 'ChapterGeneration',
    component: ChapterGeneration,
    meta: {
      title: '章节生成',
      icon: 'Magic',
      showInMenu: true,
      category: '创作工具'
    }
  },
  {
    path: '/character-edit',
    name: 'CharacterEdit',
    component: CharacterEdit,
    meta: {
      title: '人物编辑',
      icon: 'User',
      showInMenu: true,
      category: '角色管理'
    }
  },
  {
    path: '/character-relationship',
    name: 'CharacterRelationship',
    component: CharacterRelationship,
    meta: {
      title: '人物关系图',
      icon: 'Share',
      showInMenu: true,
      category: '角色管理'
    }
  },
  {
    path: '/chapter-analysis',
    name: 'ChapterAnalysis',
    component: ChapterAnalysis,
    meta: {
      title: '章节分析',
      icon: 'DataAnalysis',
      showInMenu: true,
      category: '分析工具'
    }
  },
  {
    path: '/statistics',
    name: 'Statistics',
    component: Statistics,
    meta: {
      title: '统计信息',
      icon: 'TrendCharts',
      showInMenu: true,
      category: '分析工具'
    }
  },
  {
    path: '/ai-chat',
    name: 'AIChat',
    component: AIChat,
    meta: {
      title: 'AI聊天',
      icon: 'ChatDotRound',
      showInMenu: true,
      category: 'AI工具'
    }
  },
  {
    path: '/prompt-templates',
    name: 'PromptTemplates',
    component: PromptTemplates,
    meta: {
      title: '提示词模板',
      icon: 'Collection',
      showInMenu: true,
      category: 'AI工具'
    }
  },
  {
    path: '/ai-flavor-reduction',
    name: 'AIFlavorReduction',
    component: AIFlavorReduction,
    meta: {
      title: '降AI味',
      icon: 'Refresh',
      showInMenu: true,
      category: 'AI工具'
    }
  },
  {
    path: '/memory-window',
    name: 'MemoryWindow',
    component: MemoryWindow,
    meta: {
      title: '记忆窗口',
      icon: 'Monitor',
      showInMenu: false,
      category: '系统工具'
    }
  },
  {
    path: '/vector-search',
    name: 'VectorSearch',
    component: VectorSearch,
    meta: {
      title: '向量库检索',
      icon: 'Search',
      showInMenu: true,
      category: '高级工具'
    }
  },
  {
    path: '/context-management',
    name: 'ContextManagement',
    component: ContextManagement,
    meta: {
      title: '上下文管理',
      icon: 'Connection',
      showInMenu: true,
      category: '高级工具'
    }
  },
  {
    path: '/platform-adaptation',
    name: 'PlatformAdaptation',
    component: PlatformAdaptation,
    meta: {
      title: '平台适配',
      icon: 'Platform',
      showInMenu: true,
      category: '高级工具'
    }
  },
  {
    path: '/running-logs',
    name: 'RunningLogs',
    component: RunningLogs,
    meta: {
      title: '运行日志',
      icon: 'Document',
      showInMenu: true,
      category: '系统工具'
    }
  },
  {
    path: '/settings',
    name: 'Settings',
    component: Settings,
    meta: {
      title: '设置',
      icon: 'Setting',
      showInMenu: true,
      category: '系统工具'
    }
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHashHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta?.title) {
    document.title = `${to.meta.title} - AI小说助手`
  }
  
  next()
})

// 路由错误处理
router.onError((error) => {
  console.error('路由错误:', error)
})

export default router

// 导出路由配置供其他组件使用
export { routes }

// 根据分类获取路由
export const getRoutesByCategory = () => {
  const categories: Record<string, RouteRecordRaw[]> = {}
  
  routes.forEach(route => {
    if (route.meta?.showInMenu && route.meta?.category) {
      const category = route.meta.category as string
      if (!categories[category]) {
        categories[category] = []
      }
      categories[category].push(route)
    }
  })
  
  return categories
}

// 获取菜单路由
export const getMenuRoutes = () => {
  return routes.filter(route => route.meta?.showInMenu)
}
