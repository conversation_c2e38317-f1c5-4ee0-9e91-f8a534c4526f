# AI小说助手

基于AI的智能小说创作工具，支持多种AI模型，提供从大纲生成到章节创作的全流程辅助功能。

## 功能特色

### 🤖 AI智能生成
- 支持OpenAI、<PERSON>、Gemini、ModelScope、SiliconFlow、Ollama等多种AI模型
- 智能大纲生成，快速构建故事框架
- AI辅助章节创作，提升写作效率
- 降AI味技术，优化生成内容的自然度

### 📚 创作管理
- 完整的项目管理系统
- 章节结构化编辑
- 角色档案管理和关系图可视化
- 创作进度统计和数据分析

### 🎨 现代化界面
- Glassmorphism毛玻璃设计风格
- 明暗主题切换
- 响应式布局设计
- 流畅的交互体验

### 🔧 高级功能
- 提示词模板系统
- 向量库语义检索
- 上下文智能管理
- 网络小说平台适配

## 技术架构

- **前端**: Electron 28.x + Vue 3.4.x + TypeScript 5.x
- **UI组件**: Element Plus 2.x + Tailwind CSS 3.x
- **状态管理**: Pinia 2.x
- **数据存储**: SQLite 3.x + Better-SQLite3
- **构建工具**: Vite 5.x + Electron Builder

## 开发环境要求

- Node.js 20.x+
- npm 9.x+ 或 yarn 1.22.x+
- Git

## 快速开始

### 安装依赖

```bash
cd AI_novel
npm install
```

### 开发模式

```bash
npm run dev
```

### 构建应用

```bash
# 构建所有平台
npm run dist

# 构建特定平台
npm run dist:win    # Windows
npm run dist:mac    # macOS
npm run dist:linux  # Linux
```

## 项目结构

```
AI_novel/
├── src/
│   ├── main/           # Electron主进程
│   ├── preload/        # 预加载脚本
│   └── renderer/       # Vue渲染进程
│       ├── src/
│       │   ├── components/  # 通用组件
│       │   ├── views/       # 页面组件
│       │   ├── stores/      # 状态管理
│       │   ├── router/      # 路由配置
│       │   └── styles/      # 样式文件
│       └── index.html
├── resources/          # 资源文件
│   ├── icons/         # 应用图标
│   ├── database/      # 数据库资源
│   └── templates/     # 提示词模板
├── build/             # 构建配置
└── dist/              # 构建输出
```

## 核心功能模块

1. **项目管理** - 创建、保存、导入导出项目
2. **大纲生成** - AI驱动的智能大纲创建
3. **大纲编辑** - 大纲内容的编辑和优化
4. **章节编辑** - 章节结构管理和编辑
5. **章节生成** - AI辅助章节内容创作
6. **人物编辑** - 角色档案管理
7. **人物关系图** - 角色关系可视化
8. **章节分析** - 内容质量分析和改进建议
9. **统计信息** - 创作进度和数据统计
10. **AI聊天** - AI模型验证和写作咨询
11. **提示词模板** - 模板管理和自定义
12. **降AI味功能** - AI生成内容优化
13. **记忆窗口** - 窗口状态记忆
14. **设置** - 系统配置和API管理
15. **运行日志** - 应用运行状态记录
16. **向量库检索** - 基于语义的内容检索
17. **上下文管理** - 智能上下文提取和应用
18. **网络小说平台适配** - 针对不同平台的内容优化

## 开发指南

### 代码规范
- 使用TypeScript进行类型安全开发
- 遵循Vue 3 Composition API最佳实践
- 使用ESLint和Prettier保证代码质量
- 组件命名采用PascalCase
- 文件命名采用kebab-case

### 提交规范
- feat: 新功能
- fix: 修复bug
- docs: 文档更新
- style: 代码格式调整
- refactor: 代码重构
- test: 测试相关
- chore: 构建过程或辅助工具的变动

## 许可证

MIT License

## 贡献指南

欢迎提交Issue和Pull Request来帮助改进项目。

## 联系方式

- 项目地址: [GitHub Repository]
- 问题反馈: [Issues]
- 邮箱: [<EMAIL>]
