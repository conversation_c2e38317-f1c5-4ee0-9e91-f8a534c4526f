<template>
  <div class="home-container">
    <!-- 侧边栏 -->
    <div class="sidebar" :class="{ collapsed: appStore.sidebarCollapsed }">
      <div class="sidebar-header">
        <div class="logo" v-if="!appStore.sidebarCollapsed">
          <el-icon class="logo-icon"><EditPen /></el-icon>
          <span class="logo-text">AI小说助手</span>
        </div>
        <el-button 
          class="collapse-btn" 
          :icon="appStore.sidebarCollapsed ? Expand : Fold"
          @click="appStore.toggleSidebar"
          text
        />
      </div>
      
      <div class="sidebar-content">
        <SidebarMenu />
      </div>
    </div>

    <!-- 主内容区 -->
    <div class="main-content">
      <!-- 顶部工具栏 -->
      <div class="content-header">
        <div class="header-left">
          <h1 class="page-title">{{ currentPageTitle }}</h1>
        </div>
        <div class="header-right">
          <el-button 
            :icon="appStore.isDarkMode ? Sunny : Moon"
            @click="appStore.toggleTheme"
            circle
            text
          />
          <el-dropdown @command="handleUserAction">
            <el-button circle text>
              <el-icon><User /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="settings">
                  <el-icon><Setting /></el-icon>
                  设置
                </el-dropdown-item>
                <el-dropdown-item command="about">
                  <el-icon><InfoFilled /></el-icon>
                  关于
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>

      <!-- 内容区域 -->
      <div class="content-body">
        <router-view v-slot="{ Component }">
          <transition name="fade" mode="out-in">
            <component :is="Component" />
          </transition>
        </router-view>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElButton, ElIcon, ElDropdown, ElDropdownMenu, ElDropdownItem, ElMessage } from 'element-plus'
import { 
  EditPen, Expand, Fold, Sunny, Moon, User, Setting, InfoFilled 
} from '@element-plus/icons-vue'
import { useAppStore } from '../stores/app'
import SidebarMenu from '../components/SidebarMenu.vue'

const route = useRoute()
const router = useRouter()
const appStore = useAppStore()

// 当前页面标题
const currentPageTitle = computed(() => {
  return route.meta?.title as string || '首页'
})

// 用户操作处理
const handleUserAction = (command: string) => {
  switch (command) {
    case 'settings':
      router.push('/settings')
      break
    case 'about':
      showAboutDialog()
      break
  }
}

// 显示关于对话框
const showAboutDialog = async () => {
  try {
    const appName = await window.electronAPI.app.getName()
    const appVersion = await window.electronAPI.app.getVersion()
    
    ElMessage.info(`${appName} v${appVersion}`)
  } catch (error) {
    console.error('获取应用信息失败:', error)
    ElMessage.error('获取应用信息失败')
  }
}

// 初始化
onMounted(async () => {
  await appStore.initializeApp()
})
</script>

<style scoped>
.home-container {
  @apply layout-container;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.sidebar {
  @apply glass-container;
  display: flex;
  flex-direction: column;
  margin: 16px;
  margin-right: 8px;
}

.sidebar-header {
  @apply flex items-center justify-between p-4 border-b border-white/20;
}

.logo {
  @apply flex items-center;
}

.logo-icon {
  @apply text-2xl text-white mr-3;
}

.logo-text {
  @apply text-lg font-semibold text-white;
}

.collapse-btn {
  @apply text-white;
}

.sidebar-content {
  @apply flex-1 overflow-auto p-2;
}

.main-content {
  @apply flex-1 flex flex-col;
  margin: 16px;
  margin-left: 8px;
}

.content-header {
  @apply glass-panel flex items-center justify-between mb-4;
  height: 60px;
}

.header-left {
  @apply flex items-center;
}

.page-title {
  @apply text-xl font-semibold text-white m-0;
}

.header-right {
  @apply flex items-center space-x-2;
}

.header-right .el-button {
  @apply text-white;
}

.content-body {
  @apply flex-1 overflow-auto;
}

/* 路由过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
