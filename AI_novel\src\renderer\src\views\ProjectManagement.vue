<template>
  <div class="page-container">
    <div class="page-content glass-card">
      <div class="page-header">
        <h1 class="page-title">项目管理</h1>
        <p class="page-description">创建、管理和组织您的小说项目</p>
      </div>
      
      <div class="content-placeholder">
        <el-icon class="placeholder-icon"><FolderOpened /></el-icon>
        <h3>项目管理功能</h3>
        <p>此功能正在开发中...</p>
        <el-button type="primary" @click="$router.push('/')">返回首页</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ElIcon, ElButton } from 'element-plus'
import { FolderOpened } from '@element-plus/icons-vue'
</script>

<style scoped>
.page-container {
  @apply p-6;
}

.page-content {
  @apply max-w-4xl mx-auto;
}

.page-header {
  @apply text-center mb-8;
}

.page-title {
  @apply text-3xl font-bold text-white mb-2;
}

.page-description {
  @apply text-white/70;
}

.content-placeholder {
  @apply text-center py-16;
}

.placeholder-icon {
  @apply text-6xl text-white/50 mb-4;
}

.content-placeholder h3 {
  @apply text-xl font-semibold text-white mb-2;
}

.content-placeholder p {
  @apply text-white/70 mb-6;
}
</style>
