import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron'

// 定义API接口类型
export interface ElectronAPI {
  // 应用信息
  app: {
    getVersion: () => Promise<string>
    getName: () => Promise<string>
    getPath: (name: string) => Promise<string>
  }

  // 窗口操作
  window: {
    minimize: () => Promise<void>
    maximize: () => Promise<void>
    close: () => Promise<void>
    isMaximized: () => Promise<boolean>
  }

  // 数据库操作
  database: {
    query: (sql: string, params?: any[]) => Promise<any[]>
    run: (sql: string, params?: any[]) => Promise<{ changes: number; lastInsertRowid: number }>
    get: (sql: string, params?: any[]) => Promise<any>
  }

  // 文件操作
  file: {
    openProject: () => Promise<{ success: boolean; data?: any; error?: string }>
    saveProject: (projectData: any) => Promise<{ success: boolean; filePath?: string; error?: string }>
    exportProject: (projectData: any, format: string) => Promise<{ success: boolean; filePath?: string; error?: string }>
  }

  // AI服务
  ai: {
    generate: (prompt: string, config: any) => Promise<any>
    testConnection: (config: any) => Promise<{ success: boolean; error?: string }>
  }

  // 系统操作
  system: {
    openExternal: (url: string) => Promise<void>
    showItemInFolder: (path: string) => Promise<void>
  }

  // 事件监听
  on: (channel: string, callback: (...args: any[]) => void) => void
  off: (channel: string, callback: (...args: any[]) => void) => void
}

// 暴露安全的API到渲染进程
const electronAPI: ElectronAPI = {
  // 应用信息
  app: {
    getVersion: () => ipcRenderer.invoke('app:getVersion'),
    getName: () => ipcRenderer.invoke('app:getName'),
    getPath: (name: string) => ipcRenderer.invoke('app:getPath', name)
  },

  // 窗口操作
  window: {
    minimize: () => ipcRenderer.invoke('window:minimize'),
    maximize: () => ipcRenderer.invoke('window:maximize'),
    close: () => ipcRenderer.invoke('window:close'),
    isMaximized: () => ipcRenderer.invoke('window:isMaximized')
  },

  // 数据库操作
  database: {
    query: (sql: string, params?: any[]) => ipcRenderer.invoke('db:query', sql, params),
    run: (sql: string, params?: any[]) => ipcRenderer.invoke('db:run', sql, params),
    get: (sql: string, params?: any[]) => ipcRenderer.invoke('db:get', sql, params)
  },

  // 文件操作
  file: {
    openProject: () => ipcRenderer.invoke('file:openProject'),
    saveProject: (projectData: any) => ipcRenderer.invoke('file:saveProject', projectData),
    exportProject: (projectData: any, format: string) => ipcRenderer.invoke('file:exportProject', projectData, format)
  },

  // AI服务
  ai: {
    generate: (prompt: string, config: any) => ipcRenderer.invoke('ai:generate', prompt, config),
    testConnection: (config: any) => ipcRenderer.invoke('ai:testConnection', config)
  },

  // 系统操作
  system: {
    openExternal: (url: string) => ipcRenderer.invoke('system:openExternal', url),
    showItemInFolder: (path: string) => ipcRenderer.invoke('system:showItemInFolder', path)
  },

  // 事件监听
  on: (channel: string, callback: (...args: any[]) => void) => {
    // 只允许特定的事件频道
    const allowedChannels = [
      'menu:newProject',
      'menu:openProject', 
      'menu:saveProject',
      'app:update-available',
      'app:update-downloaded'
    ]

    if (allowedChannels.includes(channel)) {
      ipcRenderer.on(channel, callback)
    }
  },

  off: (channel: string, callback: (...args: any[]) => void) => {
    ipcRenderer.off(channel, callback)
  }
}

// 暴露API到全局对象
contextBridge.exposeInMainWorld('electronAPI', electronAPI)

// 类型声明
declare global {
  interface Window {
    electronAPI: ElectronAPI
  }
}

// 页面加载完成后的初始化
window.addEventListener('DOMContentLoaded', () => {
  console.log('预加载脚本已加载')
  
  // 可以在这里添加一些初始化逻辑
  // 比如设置全局错误处理、性能监控等
  
  // 全局错误处理
  window.addEventListener('error', (event) => {
    console.error('渲染进程错误:', event.error)
  })

  window.addEventListener('unhandledrejection', (event) => {
    console.error('未处理的Promise拒绝:', event.reason)
  })
})

// 导出类型供TypeScript使用
export type { ElectronAPI }
