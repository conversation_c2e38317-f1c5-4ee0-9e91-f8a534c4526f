import { BrowserWindow, screen, app } from 'electron'
import { join } from 'path'

export class WindowManager {
  private mainWindow: BrowserWindow | null = null
  private isDevelopment = process.env.NODE_ENV === 'development'

  constructor() {
    // 构造函数
  }

  public createMainWindow(): BrowserWindow {
    // 获取主显示器信息
    const primaryDisplay = screen.getPrimaryDisplay()
    const { width, height } = primaryDisplay.workAreaSize

    // 计算窗口大小和位置
    const windowWidth = Math.min(1400, Math.floor(width * 0.8))
    const windowHeight = Math.min(900, Math.floor(height * 0.8))
    const windowX = Math.floor((width - windowWidth) / 2)
    const windowY = Math.floor((height - windowHeight) / 2)

    // 创建主窗口
    this.mainWindow = new BrowserWindow({
      width: windowWidth,
      height: windowHeight,
      x: windowX,
      y: windowY,
      minWidth: 1000,
      minHeight: 700,
      show: false, // 先不显示，等加载完成后再显示
      frame: true,
      titleBarStyle: process.platform === 'darwin' ? 'hiddenInset' : 'default',
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        enableRemoteModule: false,
        preload: join(__dirname, '../preload/index.js'),
        webSecurity: true,
        allowRunningInsecureContent: false,
        experimentalFeatures: false
      },
      icon: this.getAppIcon()
    })

    // 加载应用
    this.loadApplication()

    // 设置窗口事件
    this.setupWindowEvents()

    return this.mainWindow
  }

  private loadApplication() {
    if (!this.mainWindow) return

    if (this.isDevelopment) {
      // 开发环境加载本地服务器
      this.mainWindow.loadURL('http://localhost:5173')
      
      // 开发环境下打开开发者工具
      this.mainWindow.webContents.openDevTools()
    } else {
      // 生产环境加载本地文件
      this.mainWindow.loadFile(join(__dirname, '../renderer/index.html'))
    }
  }

  private setupWindowEvents() {
    if (!this.mainWindow) return

    // 窗口准备显示
    this.mainWindow.once('ready-to-show', () => {
      if (this.mainWindow) {
        this.mainWindow.show()
        
        // 开发环境下聚焦窗口
        if (this.isDevelopment) {
          this.mainWindow.focus()
        }
      }
    })

    // 窗口关闭
    this.mainWindow.on('closed', () => {
      this.mainWindow = null
    })

    // 阻止新窗口打开
    this.mainWindow.webContents.setWindowOpenHandler(({ url }) => {
      // 在外部浏览器中打开链接
      require('electron').shell.openExternal(url)
      return { action: 'deny' }
    })

    // 阻止导航到外部URL
    this.mainWindow.webContents.on('will-navigate', (event, navigationUrl) => {
      const parsedUrl = new URL(navigationUrl)
      
      // 只允许本地URL
      if (parsedUrl.origin !== 'http://localhost:5173' && parsedUrl.protocol !== 'file:') {
        event.preventDefault()
      }
    })

    // 页面加载完成
    this.mainWindow.webContents.once('dom-ready', () => {
      console.log('渲染进程加载完成')
    })

    // 处理页面崩溃
    this.mainWindow.webContents.on('crashed', () => {
      console.error('渲染进程崩溃')
      
      if (this.mainWindow) {
        const options = {
          type: 'error' as const,
          title: '应用崩溃',
          message: '应用遇到了一个错误并崩溃了。',
          buttons: ['重新加载', '退出']
        }
        
        require('electron').dialog.showMessageBox(this.mainWindow, options).then((result) => {
          if (result.response === 0) {
            // 重新加载
            this.mainWindow?.reload()
          } else {
            // 退出应用
            app.quit()
          }
        })
      }
    })

    // 处理无响应
    this.mainWindow.on('unresponsive', () => {
      console.warn('窗口无响应')
    })

    this.mainWindow.on('responsive', () => {
      console.log('窗口恢复响应')
    })
  }

  private getAppIcon(): string {
    const iconPath = join(__dirname, '../../resources/icons')
    
    switch (process.platform) {
      case 'win32':
        return join(iconPath, 'icon.ico')
      case 'darwin':
        return join(iconPath, 'icon.icns')
      default:
        return join(iconPath, 'icon.png')
    }
  }

  // 公共方法
  public getMainWindow(): BrowserWindow | null {
    return this.mainWindow
  }

  public minimizeWindow(): void {
    if (this.mainWindow) {
      this.mainWindow.minimize()
    }
  }

  public maximizeWindow(): void {
    if (this.mainWindow) {
      if (this.mainWindow.isMaximized()) {
        this.mainWindow.unmaximize()
      } else {
        this.mainWindow.maximize()
      }
    }
  }

  public closeWindow(): void {
    if (this.mainWindow) {
      this.mainWindow.close()
    }
  }

  public isMaximized(): boolean {
    return this.mainWindow ? this.mainWindow.isMaximized() : false
  }

  public focusWindow(): void {
    if (this.mainWindow) {
      if (this.mainWindow.isMinimized()) {
        this.mainWindow.restore()
      }
      this.mainWindow.focus()
    }
  }

  public reloadWindow(): void {
    if (this.mainWindow) {
      this.mainWindow.reload()
    }
  }

  public toggleDevTools(): void {
    if (this.mainWindow) {
      this.mainWindow.webContents.toggleDevTools()
    }
  }
}
