import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export interface AppInfo {
  name: string
  version: string
}

export interface Theme {
  mode: 'light' | 'dark'
  primaryColor: string
  accentColor: string
}

export interface WindowState {
  isMaximized: boolean
  width: number
  height: number
}

export const useAppStore = defineStore('app', () => {
  // 应用信息
  const appInfo = ref<AppInfo>({
    name: 'AI小说助手',
    version: '1.0.0'
  })

  // 加载状态
  const isLoading = ref(false)
  const loadingText = ref('加载中...')

  // 主题设置
  const theme = ref<Theme>({
    mode: 'light',
    primaryColor: '#2563eb',
    accentColor: '#10b981'
  })

  // 窗口状态
  const windowState = ref<WindowState>({
    isMaximized: false,
    width: 1400,
    height: 900
  })

  // 侧边栏状态
  const sidebarCollapsed = ref(false)

  // 当前项目ID
  const currentProjectId = ref<number | null>(null)

  // 计算属性
  const isDarkMode = computed(() => theme.value.mode === 'dark')
  const hasCurrentProject = computed(() => currentProjectId.value !== null)

  // 方法
  const setAppInfo = (info: Partial<AppInfo>) => {
    appInfo.value = { ...appInfo.value, ...info }
  }

  const setLoading = (loading: boolean, text?: string) => {
    isLoading.value = loading
    if (text) {
      loadingText.value = text
    }
  }

  const setTheme = (newTheme: Partial<Theme>) => {
    theme.value = { ...theme.value, ...newTheme }
    
    // 应用主题到DOM
    const root = document.documentElement
    root.classList.toggle('dark', newTheme.mode === 'dark')
    
    if (newTheme.primaryColor) {
      root.style.setProperty('--el-color-primary', newTheme.primaryColor)
    }
    
    if (newTheme.accentColor) {
      root.style.setProperty('--el-color-success', newTheme.accentColor)
    }
  }

  const toggleTheme = () => {
    const newMode = theme.value.mode === 'light' ? 'dark' : 'light'
    setTheme({ mode: newMode })
  }

  const setWindowState = (state: Partial<WindowState>) => {
    windowState.value = { ...windowState.value, ...state }
  }

  const toggleSidebar = () => {
    sidebarCollapsed.value = !sidebarCollapsed.value
  }

  const setSidebarCollapsed = (collapsed: boolean) => {
    sidebarCollapsed.value = collapsed
  }

  const setCurrentProject = (projectId: number | null) => {
    currentProjectId.value = projectId
  }

  // 初始化应用设置
  const initializeApp = async () => {
    try {
      // 从本地存储加载设置
      const savedTheme = localStorage.getItem('app-theme')
      if (savedTheme) {
        const parsedTheme = JSON.parse(savedTheme)
        setTheme(parsedTheme)
      }

      const savedSidebarState = localStorage.getItem('sidebar-collapsed')
      if (savedSidebarState) {
        sidebarCollapsed.value = JSON.parse(savedSidebarState)
      }

      const savedProjectId = localStorage.getItem('current-project-id')
      if (savedProjectId) {
        currentProjectId.value = parseInt(savedProjectId)
      }

      console.log('应用设置初始化完成')
    } catch (error) {
      console.error('应用设置初始化失败:', error)
    }
  }

  // 保存设置到本地存储
  const saveSettings = () => {
    try {
      localStorage.setItem('app-theme', JSON.stringify(theme.value))
      localStorage.setItem('sidebar-collapsed', JSON.stringify(sidebarCollapsed.value))
      
      if (currentProjectId.value) {
        localStorage.setItem('current-project-id', currentProjectId.value.toString())
      } else {
        localStorage.removeItem('current-project-id')
      }

      console.log('应用设置已保存')
    } catch (error) {
      console.error('保存应用设置失败:', error)
    }
  }

  return {
    // 状态
    appInfo,
    isLoading,
    loadingText,
    theme,
    windowState,
    sidebarCollapsed,
    currentProjectId,
    
    // 计算属性
    isDarkMode,
    hasCurrentProject,
    
    // 方法
    setAppInfo,
    setLoading,
    setTheme,
    toggleTheme,
    setWindowState,
    toggleSidebar,
    setSidebarCollapsed,
    setCurrentProject,
    initializeApp,
    saveSettings
  }
})
