import { dialog, app } from 'electron'
import { readFileSync, writeFileSync, existsSync } from 'fs'
import { join, extname, basename } from 'path'

export interface ProjectData {
  id?: number
  title: string
  genre: string
  theme: string
  style: string
  totalChapters: number
  wordsPerChapter: number
  outline?: any
  chapters?: any[]
  characters?: any[]
  createdAt?: string
  updatedAt?: string
}

export class FileManager {
  private readonly supportedFormats = ['.ainovel', '.json', '.txt', '.docx']

  constructor() {
    // 构造函数
  }

  public async openProject(): Promise<{ success: boolean; data?: ProjectData; error?: string }> {
    try {
      const result = await dialog.showOpenDialog({
        title: '打开项目',
        defaultPath: app.getPath('documents'),
        filters: [
          { name: 'AI小说项目', extensions: ['ainovel'] },
          { name: 'JSON文件', extensions: ['json'] },
          { name: '所有文件', extensions: ['*'] }
        ],
        properties: ['openFile']
      })

      if (result.canceled || !result.filePaths.length) {
        return { success: false, error: '用户取消操作' }
      }

      const filePath = result.filePaths[0]
      const fileExt = extname(filePath).toLowerCase()

      if (!this.supportedFormats.includes(fileExt)) {
        return { success: false, error: '不支持的文件格式' }
      }

      const projectData = await this.loadProjectFile(filePath)
      return { success: true, data: projectData }

    } catch (error) {
      console.error('打开项目失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '打开项目失败'
      }
    }
  }

  public async saveProject(projectData: ProjectData): Promise<{ success: boolean; filePath?: string; error?: string }> {
    try {
      const result = await dialog.showSaveDialog({
        title: '保存项目',
        defaultPath: join(app.getPath('documents'), `${projectData.title || '未命名项目'}.ainovel`),
        filters: [
          { name: 'AI小说项目', extensions: ['ainovel'] },
          { name: 'JSON文件', extensions: ['json'] }
        ]
      })

      if (result.canceled || !result.filePath) {
        return { success: false, error: '用户取消操作' }
      }

      await this.saveProjectFile(result.filePath, projectData)
      return { success: true, filePath: result.filePath }

    } catch (error) {
      console.error('保存项目失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '保存项目失败'
      }
    }
  }

  public async exportProject(projectData: ProjectData, format: string): Promise<{ success: boolean; filePath?: string; error?: string }> {
    try {
      let filters: Electron.FileFilter[]
      let defaultExt: string

      switch (format.toLowerCase()) {
        case 'txt':
          filters = [{ name: '文本文件', extensions: ['txt'] }]
          defaultExt = 'txt'
          break
        case 'json':
          filters = [{ name: 'JSON文件', extensions: ['json'] }]
          defaultExt = 'json'
          break
        case 'docx':
          filters = [{ name: 'Word文档', extensions: ['docx'] }]
          defaultExt = 'docx'
          break
        default:
          return { success: false, error: '不支持的导出格式' }
      }

      const result = await dialog.showSaveDialog({
        title: '导出项目',
        defaultPath: join(app.getPath('documents'), `${projectData.title || '未命名项目'}.${defaultExt}`),
        filters
      })

      if (result.canceled || !result.filePath) {
        return { success: false, error: '用户取消操作' }
      }

      await this.exportProjectFile(result.filePath, projectData, format)
      return { success: true, filePath: result.filePath }

    } catch (error) {
      console.error('导出项目失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '导出项目失败'
      }
    }
  }

  private async loadProjectFile(filePath: string): Promise<ProjectData> {
    if (!existsSync(filePath)) {
      throw new Error('文件不存在')
    }

    const fileExt = extname(filePath).toLowerCase()
    const fileContent = readFileSync(filePath, 'utf-8')

    switch (fileExt) {
      case '.ainovel':
      case '.json':
        try {
          const data = JSON.parse(fileContent)
          return this.validateProjectData(data)
        } catch (error) {
          throw new Error('JSON格式错误')
        }

      case '.txt':
        // 简单文本格式解析
        return this.parseTextFile(fileContent, basename(filePath, '.txt'))

      default:
        throw new Error('不支持的文件格式')
    }
  }

  private async saveProjectFile(filePath: string, projectData: ProjectData): Promise<void> {
    const fileExt = extname(filePath).toLowerCase()

    // 添加元数据
    const dataWithMeta = {
      ...projectData,
      version: '1.0',
      exportedAt: new Date().toISOString(),
      exportedBy: 'AI小说助手'
    }

    switch (fileExt) {
      case '.ainovel':
      case '.json':
        writeFileSync(filePath, JSON.stringify(dataWithMeta, null, 2), 'utf-8')
        break

      default:
        throw new Error('不支持的保存格式')
    }
  }

  private async exportProjectFile(filePath: string, projectData: ProjectData, format: string): Promise<void> {
    switch (format.toLowerCase()) {
      case 'txt':
        await this.exportToText(filePath, projectData)
        break

      case 'json':
        await this.exportToJson(filePath, projectData)
        break

      case 'docx':
        await this.exportToDocx(filePath, projectData)
        break

      default:
        throw new Error('不支持的导出格式')
    }
  }

  private async exportToText(filePath: string, projectData: ProjectData): Promise<void> {
    let content = `${projectData.title}\n`
    content += `${'='.repeat(projectData.title.length)}\n\n`
    
    content += `类型：${projectData.genre}\n`
    content += `主题：${projectData.theme}\n`
    content += `风格：${projectData.style}\n`
    content += `总章节数：${projectData.totalChapters}\n`
    content += `每章字数：${projectData.wordsPerChapter}\n\n`

    if (projectData.outline) {
      content += `大纲\n----\n`
      content += `核心主题：${projectData.outline.coreTheme || ''}\n`
      content += `故事梗概：${projectData.outline.storySummary || ''}\n`
      content += `世界观设定：${projectData.outline.worldSetting || ''}\n\n`
    }

    if (projectData.chapters && projectData.chapters.length > 0) {
      content += `章节内容\n--------\n\n`
      for (const chapter of projectData.chapters) {
        content += `第${chapter.chapterNumber}章 ${chapter.title}\n`
        content += `${'-'.repeat(20)}\n`
        content += `${chapter.content || chapter.summary || ''}\n\n`
      }
    }

    if (projectData.characters && projectData.characters.length > 0) {
      content += `角色设定\n--------\n\n`
      for (const character of projectData.characters) {
        content += `${character.name}（${character.roleType}）\n`
        content += `身份：${character.identity || ''}\n`
        content += `性格：${character.personality || ''}\n`
        content += `背景：${character.background || ''}\n`
        content += `外貌：${character.appearance || ''}\n\n`
      }
    }

    writeFileSync(filePath, content, 'utf-8')
  }

  private async exportToJson(filePath: string, projectData: ProjectData): Promise<void> {
    const exportData = {
      ...projectData,
      exportedAt: new Date().toISOString(),
      exportedBy: 'AI小说助手',
      version: '1.0'
    }

    writeFileSync(filePath, JSON.stringify(exportData, null, 2), 'utf-8')
  }

  private async exportToDocx(filePath: string, projectData: ProjectData): Promise<void> {
    // 简化的DOCX导出，实际应该使用docx库
    // 这里先导出为文本格式
    await this.exportToText(filePath.replace('.docx', '.txt'), projectData)
    console.log('DOCX导出功能待完善，已导出为TXT格式')
  }

  private validateProjectData(data: any): ProjectData {
    if (!data || typeof data !== 'object') {
      throw new Error('无效的项目数据')
    }

    return {
      id: data.id,
      title: data.title || '未命名项目',
      genre: data.genre || '',
      theme: data.theme || '',
      style: data.style || '',
      totalChapters: Number(data.totalChapters) || 1,
      wordsPerChapter: Number(data.wordsPerChapter) || 3000,
      outline: data.outline,
      chapters: Array.isArray(data.chapters) ? data.chapters : [],
      characters: Array.isArray(data.characters) ? data.characters : [],
      createdAt: data.createdAt,
      updatedAt: data.updatedAt
    }
  }

  private parseTextFile(content: string, filename: string): ProjectData {
    // 简单的文本文件解析
    const lines = content.split('\n')
    const title = lines[0]?.trim() || filename

    return {
      title,
      genre: '',
      theme: '',
      style: '',
      totalChapters: 1,
      wordsPerChapter: 3000,
      outline: {
        coreTheme: '',
        storySummary: content.substring(0, 500),
        worldSetting: ''
      },
      chapters: [{
        chapterNumber: 1,
        title: '第一章',
        content: content,
        wordCount: content.length
      }],
      characters: []
    }
  }
}
