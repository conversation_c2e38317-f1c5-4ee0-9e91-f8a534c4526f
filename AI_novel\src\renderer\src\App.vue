<template>
  <div id="app" class="app-container">
    <!-- 自定义标题栏 -->
    <div class="title-bar" v-if="!isMaximized">
      <div class="title-bar-content">
        <div class="app-title">
          <el-icon class="app-icon"><EditPen /></el-icon>
          <span>AI小说助手</span>
        </div>
        <div class="window-controls">
          <button class="control-btn minimize" @click="minimizeWindow">
            <el-icon><Minus /></el-icon>
          </button>
          <button class="control-btn maximize" @click="maximizeWindow">
            <el-icon><FullScreen /></el-icon>
          </button>
          <button class="control-btn close" @click="closeWindow">
            <el-icon><Close /></el-icon>
          </button>
        </div>
      </div>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content" :class="{ 'maximized': isMaximized }">
      <router-view />
    </div>

    <!-- 全局加载遮罩 -->
    <div v-if="appStore.isLoading" class="global-loading">
      <div class="loading-content">
        <el-icon class="loading-icon"><Loading /></el-icon>
        <p>{{ appStore.loadingText }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { ElIcon, ElMessage } from 'element-plus'
import { EditPen, Minus, FullScreen, Close, Loading } from '@element-plus/icons-vue'
import { useAppStore } from './stores/app'

const appStore = useAppStore()
const isMaximized = ref(false)

// 窗口控制方法
const minimizeWindow = async () => {
  try {
    await window.electronAPI.window.minimize()
  } catch (error) {
    console.error('最小化窗口失败:', error)
  }
}

const maximizeWindow = async () => {
  try {
    await window.electronAPI.window.maximize()
    isMaximized.value = await window.electronAPI.window.isMaximized()
  } catch (error) {
    console.error('最大化窗口失败:', error)
  }
}

const closeWindow = async () => {
  try {
    await window.electronAPI.window.close()
  } catch (error) {
    console.error('关闭窗口失败:', error)
  }
}

// 菜单事件处理
const handleMenuEvents = () => {
  window.electronAPI.on('menu:newProject', () => {
    console.log('新建项目')
    // 这里可以触发新建项目的逻辑
  })

  window.electronAPI.on('menu:openProject', () => {
    console.log('打开项目')
    // 这里可以触发打开项目的逻辑
  })

  window.electronAPI.on('menu:saveProject', () => {
    console.log('保存项目')
    // 这里可以触发保存项目的逻辑
  })
}

// 初始化应用
const initializeApp = async () => {
  try {
    appStore.setLoading(true, '正在初始化应用...')

    // 获取应用信息
    const version = await window.electronAPI.app.getVersion()
    const name = await window.electronAPI.app.getName()
    
    appStore.setAppInfo({ version, name })

    // 检查窗口状态
    isMaximized.value = await window.electronAPI.window.isMaximized()

    // 设置菜单事件监听
    handleMenuEvents()

    console.log(`${name} v${version} 初始化完成`)
    
    appStore.setLoading(false)
  } catch (error) {
    console.error('应用初始化失败:', error)
    ElMessage.error('应用初始化失败')
    appStore.setLoading(false)
  }
}

onMounted(() => {
  initializeApp()
})

onUnmounted(() => {
  // 清理事件监听
  window.electronAPI.off('menu:newProject', () => {})
  window.electronAPI.off('menu:openProject', () => {})
  window.electronAPI.off('menu:saveProject', () => {})
})
</script>

<style scoped>
.app-container {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow: hidden;
}

.title-bar {
  height: 32px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  -webkit-app-region: drag;
  user-select: none;
}

.title-bar-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  padding: 0 16px;
}

.app-title {
  display: flex;
  align-items: center;
  color: white;
  font-size: 14px;
  font-weight: 500;
}

.app-icon {
  margin-right: 8px;
  font-size: 16px;
}

.window-controls {
  display: flex;
  -webkit-app-region: no-drag;
}

.control-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.control-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

.control-btn.close:hover {
  background: #e81123;
}

.main-content {
  flex: 1;
  overflow: hidden;
}

.main-content.maximized {
  height: 100vh;
}

.global-loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  text-align: center;
  color: white;
}

.loading-icon {
  font-size: 32px;
  margin-bottom: 16px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
