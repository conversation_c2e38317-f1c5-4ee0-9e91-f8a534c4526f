import Database from 'better-sqlite3'
import { app } from 'electron'
import { join } from 'path'
import { existsSync, mkdirSync } from 'fs'

export class DatabaseManager {
  private db: Database.Database | null = null
  private dbPath: string

  constructor() {
    // 获取用户数据目录
    const userDataPath = app.getPath('userData')
    const dbDir = join(userDataPath, 'database')
    
    // 确保数据库目录存在
    if (!existsSync(dbDir)) {
      mkdirSync(dbDir, { recursive: true })
    }
    
    this.dbPath = join(dbDir, 'ai-novel.db')
  }

  public async initialize(): Promise<void> {
    try {
      // 创建数据库连接
      this.db = new Database(this.dbPath)
      
      // 设置数据库选项
      this.db.pragma('journal_mode = WAL')
      this.db.pragma('synchronous = NORMAL')
      this.db.pragma('cache_size = 1000')
      this.db.pragma('temp_store = MEMORY')
      
      // 创建表结构
      await this.createTables()
      
      // 插入初始数据
      await this.insertInitialData()
      
      console.log('数据库初始化成功:', this.dbPath)
    } catch (error) {
      console.error('数据库初始化失败:', error)
      throw error
    }
  }

  private async createTables(): Promise<void> {
    if (!this.db) throw new Error('数据库未初始化')

    // 项目表
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS projects (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title VARCHAR(255) NOT NULL,
        genre VARCHAR(100),
        theme VARCHAR(100),
        style VARCHAR(100),
        total_chapters INTEGER DEFAULT 1,
        words_per_chapter INTEGER DEFAULT 3000,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // 大纲表
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS outlines (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        project_id INTEGER NOT NULL,
        title VARCHAR(255),
        core_theme TEXT,
        story_summary TEXT,
        world_setting TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE
      )
    `)

    // 章节表
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS chapters (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        project_id INTEGER NOT NULL,
        chapter_number INTEGER NOT NULL,
        title VARCHAR(255),
        summary TEXT,
        content TEXT,
        word_count INTEGER DEFAULT 0,
        status VARCHAR(20) DEFAULT 'draft',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE
      )
    `)

    // 角色表
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS characters (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        project_id INTEGER NOT NULL,
        name VARCHAR(100) NOT NULL,
        role_type VARCHAR(50),
        identity VARCHAR(100),
        personality TEXT,
        background TEXT,
        appearance TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE
      )
    `)

    // 角色关系表
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS character_relationships (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        project_id INTEGER NOT NULL,
        character1_id INTEGER NOT NULL,
        character2_id INTEGER NOT NULL,
        relationship VARCHAR(100),
        description TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
        FOREIGN KEY (character1_id) REFERENCES characters(id) ON DELETE CASCADE,
        FOREIGN KEY (character2_id) REFERENCES characters(id) ON DELETE CASCADE
      )
    `)

    // 提示词模板表
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS prompt_templates (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        category VARCHAR(100),
        content TEXT NOT NULL,
        is_builtin BOOLEAN DEFAULT FALSE,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // AI配置表
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS ai_configs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name VARCHAR(255) NOT NULL,
        provider VARCHAR(50) NOT NULL,
        api_key TEXT,
        model_name VARCHAR(100),
        api_url VARCHAR(255),
        is_active BOOLEAN DEFAULT FALSE,
        status VARCHAR(20) DEFAULT 'untested',
        last_tested DATETIME,
        tags TEXT,
        description TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // 创建索引
    this.db.exec(`
      CREATE INDEX IF NOT EXISTS idx_projects_title ON projects(title);
      CREATE INDEX IF NOT EXISTS idx_chapters_project_id ON chapters(project_id);
      CREATE INDEX IF NOT EXISTS idx_chapters_number ON chapters(chapter_number);
      CREATE INDEX IF NOT EXISTS idx_characters_project_id ON characters(project_id);
      CREATE INDEX IF NOT EXISTS idx_characters_name ON characters(name);
      CREATE INDEX IF NOT EXISTS idx_ai_configs_provider ON ai_configs(provider);
      CREATE INDEX IF NOT EXISTS idx_ai_configs_active ON ai_configs(is_active);
    `)
  }

  private async insertInitialData(): Promise<void> {
    if (!this.db) throw new Error('数据库未初始化')

    // 检查是否已有数据
    const templateCount = this.db.prepare('SELECT COUNT(*) as count FROM prompt_templates WHERE is_builtin = 1').get() as { count: number }
    
    if (templateCount.count === 0) {
      // 插入内置提示词模板
      const insertTemplate = this.db.prepare(`
        INSERT INTO prompt_templates (name, description, category, content, is_builtin)
        VALUES (?, ?, ?, ?, 1)
      `)

      const templates = [
        {
          name: '大纲生成模板',
          description: '用于生成小说大纲的标准模板',
          category: '大纲生成',
          content: `请根据以下信息生成一个详细的小说大纲：

标题：{title}
类型：{genre}
主题：{theme}
风格：{style}
章节数：{chapterCount}
每章字数：{wordsPerChapter}

请生成包含以下内容的大纲：
1. 核心主题和中心思想
2. 故事梗概（300-500字）
3. 世界观设定
4. 主要角色设定
5. 详细章节大纲（每章包含标题和主要情节）

要求：
- 情节逻辑清晰，前后呼应
- 角色性格鲜明，发展合理
- 符合{genre}类型的特点
- 体现{theme}主题
- 保持{style}的写作风格`
        },
        {
          name: '章节生成模板',
          description: '用于生成章节内容的标准模板',
          category: '章节生成',
          content: `请根据以下信息生成章节内容：

章节标题：{chapterTitle}
章节摘要：{chapterSummary}
目标字数：{targetWords}
上文内容：{previousContent}
角色信息：{characters}

要求：
1. 严格按照章节摘要展开情节
2. 字数控制在{targetWords}字左右
3. 保持与上文的连贯性
4. 角色行为符合人设
5. 语言生动，情节紧凑
6. 适当设置悬念和冲突`
        }
      ]

      for (const template of templates) {
        insertTemplate.run(template.name, template.description, template.category, template.content)
      }

      console.log('插入内置模板完成')
    }
  }

  // 数据库操作方法
  public async query(sql: string, params?: any[]): Promise<any[]> {
    if (!this.db) throw new Error('数据库未初始化')
    
    try {
      const stmt = this.db.prepare(sql)
      return stmt.all(params || [])
    } catch (error) {
      console.error('数据库查询失败:', error)
      throw error
    }
  }

  public async run(sql: string, params?: any[]): Promise<{ changes: number; lastInsertRowid: number }> {
    if (!this.db) throw new Error('数据库未初始化')
    
    try {
      const stmt = this.db.prepare(sql)
      const result = stmt.run(params || [])
      return {
        changes: result.changes,
        lastInsertRowid: Number(result.lastInsertRowid)
      }
    } catch (error) {
      console.error('数据库执行失败:', error)
      throw error
    }
  }

  public async get(sql: string, params?: any[]): Promise<any> {
    if (!this.db) throw new Error('数据库未初始化')
    
    try {
      const stmt = this.db.prepare(sql)
      return stmt.get(params || [])
    } catch (error) {
      console.error('数据库查询失败:', error)
      throw error
    }
  }

  public async close(): Promise<void> {
    if (this.db) {
      this.db.close()
      this.db = null
      console.log('数据库连接已关闭')
    }
  }

  public getDbPath(): string {
    return this.dbPath
  }
}
