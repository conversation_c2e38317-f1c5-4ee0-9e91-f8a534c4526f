<template>
  <div class="welcome-container">
    <div class="welcome-content glass-card">
      <div class="welcome-header">
        <el-icon class="welcome-icon"><EditPen /></el-icon>
        <h1 class="welcome-title">欢迎使用 AI小说助手</h1>
        <p class="welcome-subtitle">基于AI的智能小说创作工具</p>
      </div>

      <div class="feature-grid">
        <div class="feature-card glass-panel" v-for="feature in features" :key="feature.title">
          <el-icon class="feature-icon">
            <component :is="feature.icon" />
          </el-icon>
          <h3 class="feature-title">{{ feature.title }}</h3>
          <p class="feature-description">{{ feature.description }}</p>
        </div>
      </div>

      <div class="quick-actions">
        <h2 class="actions-title">快速开始</h2>
        <div class="actions-grid">
          <el-button 
            type="primary" 
            size="large"
            :icon="Plus"
            @click="createProject"
          >
            创建新项目
          </el-button>
          <el-button 
            size="large"
            :icon="FolderOpened"
            @click="openProject"
          >
            打开项目
          </el-button>
          <el-button 
            size="large"
            :icon="Setting"
            @click="openSettings"
          >
            应用设置
          </el-button>
        </div>
      </div>

      <div class="app-info">
        <p class="version-info">版本 {{ appStore.appInfo.version }}</p>
        <p class="copyright">© 2024 AI小说助手. 保留所有权利.</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { ElIcon, ElButton } from 'element-plus'
import { 
  EditPen, Plus, FolderOpened, Setting, Magic, User, 
  DataAnalysis, ChatDotRound, Collection, TrendCharts 
} from '@element-plus/icons-vue'
import { useAppStore } from '../stores/app'

const router = useRouter()
const appStore = useAppStore()

// 功能特色
const features = [
  {
    icon: 'Magic',
    title: 'AI智能生成',
    description: '支持多种AI模型，智能生成大纲、章节内容，提升创作效率'
  },
  {
    icon: 'User',
    title: '角色管理',
    description: '完善的角色档案系统，可视化人物关系图，让角色更加立体'
  },
  {
    icon: 'DataAnalysis',
    title: '内容分析',
    description: '智能分析章节质量，提供改进建议，优化创作内容'
  },
  {
    icon: 'ChatDotRound',
    title: 'AI助手',
    description: '24小时在线AI写作顾问，随时解答创作疑问'
  },
  {
    icon: 'Collection',
    title: '模板库',
    description: '丰富的提示词模板库，支持自定义，满足不同创作需求'
  },
  {
    icon: 'TrendCharts',
    title: '数据统计',
    description: '详细的创作统计，追踪写作进度，激励持续创作'
  }
]

// 快速操作
const createProject = () => {
  router.push('/project')
}

const openProject = async () => {
  try {
    const result = await window.electronAPI.file.openProject()
    if (result.success && result.data) {
      console.log('打开项目:', result.data)
      // 这里可以处理打开的项目数据
    }
  } catch (error) {
    console.error('打开项目失败:', error)
  }
}

const openSettings = () => {
  router.push('/settings')
}
</script>

<style scoped>
.welcome-container {
  @apply flex items-center justify-center min-h-full p-8;
}

.welcome-content {
  @apply max-w-4xl w-full text-center;
}

.welcome-header {
  @apply mb-12;
}

.welcome-icon {
  @apply text-6xl text-white mb-4;
}

.welcome-title {
  @apply text-4xl font-bold text-white mb-4;
}

.welcome-subtitle {
  @apply text-xl text-white/80;
}

.feature-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12;
}

.feature-card {
  @apply text-center p-6;
}

.feature-icon {
  @apply text-3xl text-white mb-4;
}

.feature-title {
  @apply text-lg font-semibold text-white mb-2;
}

.feature-description {
  @apply text-white/70 text-sm leading-relaxed;
}

.quick-actions {
  @apply mb-12;
}

.actions-title {
  @apply text-2xl font-semibold text-white mb-6;
}

.actions-grid {
  @apply flex flex-wrap justify-center gap-4;
}

.app-info {
  @apply text-white/60 text-sm space-y-1;
}

.version-info {
  @apply font-medium;
}

.copyright {
  @apply text-xs;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .welcome-content {
    @apply px-4;
  }
  
  .welcome-title {
    @apply text-3xl;
  }
  
  .welcome-subtitle {
    @apply text-lg;
  }
  
  .feature-grid {
    @apply grid-cols-1;
  }
  
  .actions-grid {
    @apply flex-col;
  }
}
</style>
