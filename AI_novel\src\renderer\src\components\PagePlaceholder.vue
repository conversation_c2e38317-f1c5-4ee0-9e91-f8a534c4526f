<template>
  <div class="page-container">
    <div class="page-content glass-card">
      <div class="page-header">
        <h1 class="page-title">{{ title }}</h1>
        <p class="page-description">{{ description }}</p>
      </div>
      
      <div class="content-placeholder">
        <el-icon class="placeholder-icon">
          <component :is="icon" />
        </el-icon>
        <h3>{{ title }}功能</h3>
        <p>此功能正在开发中，敬请期待...</p>
        <div class="placeholder-actions">
          <el-button type="primary" @click="$router.push('/')">返回首页</el-button>
          <el-button @click="$router.back()">返回上页</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ElIcon, ElButton } from 'element-plus'

interface Props {
  title: string
  description: string
  icon: string
}

defineProps<Props>()
</script>

<style scoped>
.page-container {
  @apply p-6;
}

.page-content {
  @apply max-w-4xl mx-auto;
}

.page-header {
  @apply text-center mb-8;
}

.page-title {
  @apply text-3xl font-bold text-white mb-2;
}

.page-description {
  @apply text-white/70;
}

.content-placeholder {
  @apply text-center py-16;
}

.placeholder-icon {
  @apply text-6xl text-white/50 mb-4;
}

.content-placeholder h3 {
  @apply text-xl font-semibold text-white mb-2;
}

.content-placeholder p {
  @apply text-white/70 mb-6;
}

.placeholder-actions {
  @apply space-x-4;
}
</style>
