export interface AIConfig {
  provider: string
  apiKey: string
  apiUrl: string
  model: string
  maxTokens?: number
  temperature?: number
  topP?: number
}

export interface AIResponse {
  success: boolean
  content?: string
  error?: string
  usage?: {
    promptTokens: number
    completionTokens: number
    totalTokens: number
  }
}

export class AIServiceManager {
  private readonly supportedProviders = [
    'openai',
    'anthropic', 
    'google',
    'modelscope',
    'siliconflow',
    'ollama'
  ]

  constructor() {
    // 构造函数
  }

  public async generate(prompt: string, config: AIConfig): Promise<AIResponse> {
    try {
      // 验证配置
      if (!this.validateConfig(config)) {
        return {
          success: false,
          error: '无效的AI配置'
        }
      }

      // 根据提供商调用相应的API
      switch (config.provider.toLowerCase()) {
        case 'openai':
          return await this.callOpenAI(prompt, config)
        case 'anthropic':
          return await this.callAnthropic(prompt, config)
        case 'google':
          return await this.callGoogle(prompt, config)
        case 'modelscope':
          return await this.callModelScope(prompt, config)
        case 'siliconflow':
          return await this.callSiliconFlow(prompt, config)
        case 'ollama':
          return await this.callOllama(prompt, config)
        default:
          return {
            success: false,
            error: `不支持的AI提供商: ${config.provider}`
          }
      }
    } catch (error) {
      console.error('AI生成失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  }

  public async testConnection(config: AIConfig): Promise<{ success: boolean; error?: string }> {
    try {
      const testPrompt = '你好，请回复"连接测试成功"'
      const response = await this.generate(testPrompt, config)
      
      if (response.success) {
        return { success: true }
      } else {
        return { success: false, error: response.error }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '连接测试失败'
      }
    }
  }

  private validateConfig(config: AIConfig): boolean {
    if (!config.provider || !config.apiKey || !config.model) {
      return false
    }

    if (!this.supportedProviders.includes(config.provider.toLowerCase())) {
      return false
    }

    return true
  }

  private async callOpenAI(prompt: string, config: AIConfig): Promise<AIResponse> {
    const url = config.apiUrl || 'https://api.openai.com/v1/chat/completions'
    
    const requestBody = {
      model: config.model,
      messages: [
        {
          role: 'user',
          content: prompt
        }
      ],
      max_tokens: config.maxTokens || 4000,
      temperature: config.temperature || 0.7,
      top_p: config.topP || 1
    }

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${config.apiKey}`
      },
      body: JSON.stringify(requestBody)
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(`OpenAI API错误: ${response.status} - ${errorData.error?.message || response.statusText}`)
    }

    const data = await response.json()
    
    return {
      success: true,
      content: data.choices[0]?.message?.content || '',
      usage: {
        promptTokens: data.usage?.prompt_tokens || 0,
        completionTokens: data.usage?.completion_tokens || 0,
        totalTokens: data.usage?.total_tokens || 0
      }
    }
  }

  private async callAnthropic(prompt: string, config: AIConfig): Promise<AIResponse> {
    const url = config.apiUrl || 'https://api.anthropic.com/v1/messages'
    
    const requestBody = {
      model: config.model,
      max_tokens: config.maxTokens || 4000,
      messages: [
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: config.temperature || 0.7
    }

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': config.apiKey,
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify(requestBody)
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(`Anthropic API错误: ${response.status} - ${errorData.error?.message || response.statusText}`)
    }

    const data = await response.json()
    
    return {
      success: true,
      content: data.content[0]?.text || '',
      usage: {
        promptTokens: data.usage?.input_tokens || 0,
        completionTokens: data.usage?.output_tokens || 0,
        totalTokens: (data.usage?.input_tokens || 0) + (data.usage?.output_tokens || 0)
      }
    }
  }

  private async callGoogle(prompt: string, config: AIConfig): Promise<AIResponse> {
    const url = config.apiUrl || `https://generativelanguage.googleapis.com/v1beta/models/${config.model}:generateContent?key=${config.apiKey}`
    
    const requestBody = {
      contents: [
        {
          parts: [
            {
              text: prompt
            }
          ]
        }
      ],
      generationConfig: {
        temperature: config.temperature || 0.7,
        topP: config.topP || 1,
        maxOutputTokens: config.maxTokens || 4000
      }
    }

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(`Google AI API错误: ${response.status} - ${errorData.error?.message || response.statusText}`)
    }

    const data = await response.json()
    
    return {
      success: true,
      content: data.candidates[0]?.content?.parts[0]?.text || '',
      usage: {
        promptTokens: data.usageMetadata?.promptTokenCount || 0,
        completionTokens: data.usageMetadata?.candidatesTokenCount || 0,
        totalTokens: data.usageMetadata?.totalTokenCount || 0
      }
    }
  }

  private async callModelScope(prompt: string, config: AIConfig): Promise<AIResponse> {
    // ModelScope API实现
    const url = config.apiUrl || 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation'
    
    const requestBody = {
      model: config.model,
      input: {
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ]
      },
      parameters: {
        max_tokens: config.maxTokens || 4000,
        temperature: config.temperature || 0.7,
        top_p: config.topP || 1
      }
    }

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${config.apiKey}`
      },
      body: JSON.stringify(requestBody)
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(`ModelScope API错误: ${response.status} - ${errorData.message || response.statusText}`)
    }

    const data = await response.json()
    
    return {
      success: true,
      content: data.output?.choices[0]?.message?.content || '',
      usage: {
        promptTokens: data.usage?.input_tokens || 0,
        completionTokens: data.usage?.output_tokens || 0,
        totalTokens: data.usage?.total_tokens || 0
      }
    }
  }

  private async callSiliconFlow(prompt: string, config: AIConfig): Promise<AIResponse> {
    // SiliconFlow API实现（OpenAI兼容）
    return await this.callOpenAI(prompt, {
      ...config,
      apiUrl: config.apiUrl || 'https://api.siliconflow.cn/v1/chat/completions'
    })
  }

  private async callOllama(prompt: string, config: AIConfig): Promise<AIResponse> {
    const url = config.apiUrl || 'http://localhost:11434/api/generate'
    
    const requestBody = {
      model: config.model,
      prompt: prompt,
      stream: false,
      options: {
        temperature: config.temperature || 0.7,
        top_p: config.topP || 1,
        num_predict: config.maxTokens || 4000
      }
    }

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(`Ollama API错误: ${response.status} - ${errorData.error || response.statusText}`)
    }

    const data = await response.json()
    
    return {
      success: true,
      content: data.response || '',
      usage: {
        promptTokens: 0, // Ollama不提供token统计
        completionTokens: 0,
        totalTokens: 0
      }
    }
  }
}
