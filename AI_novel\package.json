{"name": "ai-novel-assistant", "version": "1.0.0", "description": "AI小说助手 - 基于AI的智能小说创作工具", "main": "dist-main/index.js", "author": "AI Novel Team", "license": "MIT", "homepage": ".", "scripts": {"dev": "concurrently \"npm run dev:vite\" \"npm run dev:electron\"", "dev:vite": "vite", "dev:electron": "wait-on http://localhost:5173 && electron .", "build": "npm run build:renderer && npm run build:main", "build:renderer": "vite build", "build:main": "tsc -p tsconfig.main.json", "build:preload": "tsc -p tsconfig.preload.json", "pack": "npm run build && electron-builder --dir", "dist": "npm run build && electron-builder", "dist:win": "npm run build && electron-builder --win", "dist:mac": "npm run build && electron-builder --mac", "dist:linux": "npm run build && electron-builder --linux"}, "devDependencies": {"@types/node": "^20.10.0", "@vitejs/plugin-vue": "^4.5.0", "concurrently": "^8.2.2", "electron": "^28.0.0", "electron-builder": "^24.6.4", "typescript": "^5.3.0", "vite": "^5.0.0", "vue-tsc": "^1.8.25", "wait-on": "^7.2.0"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.5", "pinia": "^2.1.7", "element-plus": "^2.4.4", "@element-plus/icons-vue": "^2.3.1", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "sqlite3": "^5.1.6", "better-sqlite3": "^9.2.2"}, "build": {"appId": "com.ainovel.assistant", "productName": "AI小说助手", "directories": {"output": "dist", "buildResources": "resources"}, "files": ["dist-main/**/*", "dist-renderer/**/*", "dist-preload/**/*", "node_modules/**/*", "resources/**/*"], "extraResources": [{"from": "resources/database", "to": "database"}, {"from": "resources/templates", "to": "templates"}], "win": {"target": [{"target": "nsis", "arch": ["x64"]}], "icon": "resources/icons/icon.ico", "requestedExecutionLevel": "asInvoker"}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}], "icon": "resources/icons/icon.icns", "category": "public.app-category.productivity"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}], "icon": "resources/icons/icon.png", "category": "Office"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "AI小说助手"}}}