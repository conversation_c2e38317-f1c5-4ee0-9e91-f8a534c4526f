<template>
  <div class="page-container">
    <div class="page-content glass-card">
      <div class="page-header">
        <h1 class="page-title">应用设置</h1>
        <p class="page-description">配置AI模型、界面主题和应用偏好</p>
      </div>
      
      <div class="settings-content">
        <el-tabs v-model="activeTab" class="settings-tabs">
          <el-tab-pane label="AI配置" name="ai">
            <div class="tab-content">
              <h3>AI模型配置</h3>
              <p>配置您的AI服务提供商和模型参数</p>
              <el-button type="primary">添加AI配置</el-button>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="界面主题" name="theme">
            <div class="tab-content">
              <h3>主题设置</h3>
              <div class="theme-options">
                <el-radio-group v-model="themeMode" @change="handleThemeChange">
                  <el-radio label="light">浅色主题</el-radio>
                  <el-radio label="dark">深色主题</el-radio>
                </el-radio-group>
              </div>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="应用偏好" name="preferences">
            <div class="tab-content">
              <h3>应用偏好</h3>
              <p>自定义应用行为和界面设置</p>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="关于" name="about">
            <div class="tab-content">
              <h3>关于 AI小说助手</h3>
              <div class="about-info">
                <p><strong>版本:</strong> {{ appStore.appInfo.version }}</p>
                <p><strong>应用名称:</strong> {{ appStore.appInfo.name }}</p>
                <p><strong>技术栈:</strong> Electron + Vue 3 + TypeScript</p>
                <p><strong>开发团队:</strong> AI Novel Team</p>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElTabs, ElTabPane, ElButton, ElRadioGroup, ElRadio } from 'element-plus'
import { useAppStore } from '../stores/app'

const appStore = useAppStore()

const activeTab = ref('ai')
const themeMode = ref(appStore.theme.mode)

const handleThemeChange = (mode: 'light' | 'dark') => {
  appStore.setTheme({ mode })
}
</script>

<style scoped>
.page-container {
  @apply p-6;
}

.page-content {
  @apply max-w-4xl mx-auto;
}

.page-header {
  @apply text-center mb-8;
}

.page-title {
  @apply text-3xl font-bold text-white mb-2;
}

.page-description {
  @apply text-white/70;
}

.settings-content {
  @apply mt-8;
}

.settings-tabs :deep(.el-tabs__header) {
  @apply border-b border-white/20;
}

.settings-tabs :deep(.el-tabs__nav-wrap::after) {
  @apply bg-white/20;
}

.settings-tabs :deep(.el-tabs__item) {
  @apply text-white/70;
}

.settings-tabs :deep(.el-tabs__item.is-active) {
  @apply text-white;
}

.settings-tabs :deep(.el-tabs__active-bar) {
  @apply bg-white;
}

.tab-content {
  @apply py-6;
}

.tab-content h3 {
  @apply text-xl font-semibold text-white mb-4;
}

.tab-content p {
  @apply text-white/70 mb-6;
}

.theme-options {
  @apply space-y-4;
}

.theme-options :deep(.el-radio) {
  @apply text-white;
}

.theme-options :deep(.el-radio__label) {
  @apply text-white;
}

.about-info {
  @apply space-y-2;
}

.about-info p {
  @apply text-white/80 mb-2;
}

.about-info strong {
  @apply text-white;
}
</style>
