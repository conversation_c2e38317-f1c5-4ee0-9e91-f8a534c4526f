{"compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": false, "strict": true, "noEmit": true, "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "noEmitOnError": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true, "baseUrl": ".", "paths": {"@/*": ["src/renderer/*"], "@main/*": ["src/main/*"], "@shared/*": ["src/shared/*"]}}, "include": ["src/renderer/**/*.ts", "src/renderer/**/*.d.ts", "src/renderer/**/*.tsx", "src/renderer/**/*.vue", "src/shared/**/*.ts"], "exclude": ["node_modules", "dist", "dist-*"], "references": [{"path": "./tsconfig.main.json"}, {"path": "./tsconfig.preload.json"}]}