import { app, BrowserWindow, ipcMain, Menu, dialog, shell } from 'electron'
import { join } from 'path'
import { WindowManager } from './window'
import { DatabaseManager } from './database'
import { AIServiceManager } from './ai-service'
import { FileManager } from './file-manager'

class MainProcess {
  private windowManager: WindowManager
  private databaseManager: DatabaseManager
  private aiServiceManager: AIServiceManager
  private fileManager: FileManager

  constructor() {
    this.windowManager = new WindowManager()
    this.databaseManager = new DatabaseManager()
    this.aiServiceManager = new AIServiceManager()
    this.fileManager = new FileManager()
    
    this.initializeApp()
  }

  private initializeApp() {
    // 设置应用用户模型ID (Windows)
    if (process.platform === 'win32') {
      app.setAppUserModelId('com.ainovel.assistant')
    }

    // 应用准备就绪
    app.whenReady().then(async () => {
      await this.setupApplication()
    })

    // 所有窗口关闭
    app.on('window-all-closed', () => {
      if (process.platform !== 'darwin') {
        app.quit()
      }
    })

    // macOS激活应用
    app.on('activate', () => {
      if (BrowserWindow.getAllWindows().length === 0) {
        this.windowManager.createMainWindow()
      }
    })

    // 应用退出前清理
    app.on('before-quit', async () => {
      await this.cleanup()
    })
  }

  private async setupApplication() {
    try {
      // 初始化数据库
      await this.databaseManager.initialize()
      
      // 设置IPC处理器
      this.setupIPC()
      
      // 设置应用菜单
      this.setupMenu()
      
      // 创建主窗口
      this.windowManager.createMainWindow()
      
      console.log('AI小说助手启动成功')
    } catch (error) {
      console.error('应用启动失败:', error)
      dialog.showErrorBox('启动错误', '应用启动失败，请重试')
      app.quit()
    }
  }

  private setupIPC() {
    // 应用信息
    ipcMain.handle('app:getVersion', () => {
      return app.getVersion()
    })

    ipcMain.handle('app:getName', () => {
      return app.getName()
    })

    ipcMain.handle('app:getPath', (_, name: string) => {
      return app.getPath(name as any)
    })

    // 窗口操作
    ipcMain.handle('window:minimize', () => {
      this.windowManager.minimizeWindow()
    })

    ipcMain.handle('window:maximize', () => {
      this.windowManager.maximizeWindow()
    })

    ipcMain.handle('window:close', () => {
      this.windowManager.closeWindow()
    })

    ipcMain.handle('window:isMaximized', () => {
      return this.windowManager.isMaximized()
    })

    // 数据库操作
    ipcMain.handle('db:query', async (_, sql: string, params?: any[]) => {
      return await this.databaseManager.query(sql, params)
    })

    ipcMain.handle('db:run', async (_, sql: string, params?: any[]) => {
      return await this.databaseManager.run(sql, params)
    })

    // 文件操作
    ipcMain.handle('file:openProject', async () => {
      return await this.fileManager.openProject()
    })

    ipcMain.handle('file:saveProject', async (_, projectData: any) => {
      return await this.fileManager.saveProject(projectData)
    })

    ipcMain.handle('file:exportProject', async (_, projectData: any, format: string) => {
      return await this.fileManager.exportProject(projectData, format)
    })

    // AI服务
    ipcMain.handle('ai:generate', async (_, prompt: string, config: any) => {
      return await this.aiServiceManager.generate(prompt, config)
    })

    ipcMain.handle('ai:testConnection', async (_, config: any) => {
      return await this.aiServiceManager.testConnection(config)
    })

    // 系统操作
    ipcMain.handle('system:openExternal', async (_, url: string) => {
      await shell.openExternal(url)
    })

    ipcMain.handle('system:showItemInFolder', async (_, path: string) => {
      shell.showItemInFolder(path)
    })
  }

  private setupMenu() {
    const template: Electron.MenuItemConstructorOptions[] = [
      {
        label: '文件',
        submenu: [
          {
            label: '新建项目',
            accelerator: 'CmdOrCtrl+N',
            click: () => {
              this.windowManager.getMainWindow()?.webContents.send('menu:newProject')
            }
          },
          {
            label: '打开项目',
            accelerator: 'CmdOrCtrl+O',
            click: () => {
              this.windowManager.getMainWindow()?.webContents.send('menu:openProject')
            }
          },
          {
            label: '保存项目',
            accelerator: 'CmdOrCtrl+S',
            click: () => {
              this.windowManager.getMainWindow()?.webContents.send('menu:saveProject')
            }
          },
          { type: 'separator' },
          {
            label: '退出',
            accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
            click: () => {
              app.quit()
            }
          }
        ]
      },
      {
        label: '编辑',
        submenu: [
          { label: '撤销', accelerator: 'CmdOrCtrl+Z', role: 'undo' },
          { label: '重做', accelerator: 'Shift+CmdOrCtrl+Z', role: 'redo' },
          { type: 'separator' },
          { label: '剪切', accelerator: 'CmdOrCtrl+X', role: 'cut' },
          { label: '复制', accelerator: 'CmdOrCtrl+C', role: 'copy' },
          { label: '粘贴', accelerator: 'CmdOrCtrl+V', role: 'paste' }
        ]
      },
      {
        label: '视图',
        submenu: [
          { label: '重新加载', accelerator: 'CmdOrCtrl+R', role: 'reload' },
          { label: '强制重新加载', accelerator: 'CmdOrCtrl+Shift+R', role: 'forceReload' },
          { label: '开发者工具', accelerator: 'F12', role: 'toggleDevTools' },
          { type: 'separator' },
          { label: '实际大小', accelerator: 'CmdOrCtrl+0', role: 'resetZoom' },
          { label: '放大', accelerator: 'CmdOrCtrl+Plus', role: 'zoomIn' },
          { label: '缩小', accelerator: 'CmdOrCtrl+-', role: 'zoomOut' },
          { type: 'separator' },
          { label: '全屏', accelerator: 'F11', role: 'togglefullscreen' }
        ]
      },
      {
        label: '帮助',
        submenu: [
          {
            label: '关于',
            click: () => {
              dialog.showMessageBox(this.windowManager.getMainWindow()!, {
                type: 'info',
                title: '关于 AI小说助手',
                message: 'AI小说助手',
                detail: `版本: ${app.getVersion()}\n基于AI的智能小说创作工具`
              })
            }
          }
        ]
      }
    ]

    const menu = Menu.buildFromTemplate(template)
    Menu.setApplicationMenu(menu)
  }

  private async cleanup() {
    try {
      await this.databaseManager.close()
      console.log('应用清理完成')
    } catch (error) {
      console.error('应用清理失败:', error)
    }
  }
}

// 启动应用
new MainProcess()
